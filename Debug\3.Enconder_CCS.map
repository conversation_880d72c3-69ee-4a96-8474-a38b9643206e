******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 12:47:39 2025

OUTPUT FILE NAME:   <3.Enconder_CCS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000d91


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00000f98  00007068  R  X
  SRAM                  20200000   00004000  0000043c  00003bc4  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000f98   00000f98    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000e38   00000e38    r-x .text
  00000ef8    00000ef8    00000070   00000070    r-- .rodata
  00000f68    00000f68    00000030   00000030    r-- .cinit
20200000    20200000    0000023c   00000000    rw-
  20200000    20200000    00000200   00000000    rw- .bss
  20200200    20200200    0000003c   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000e38     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000448    000000a8     bsp_enconder.o (.text.GROUP1_IRQHandler)
                  000004f0    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000058c    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000626    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000628    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L1_init)
                  000006b8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_L2_init)
                  00000748    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R1_init)
                  000007d8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_R2_init)
                  00000868    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000008e4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_MYUART_init)
                  00000954    0000006c     main.o (.text.main)
                  000009c0    00000062     libc.a : memset16.S.obj (.text:memset)
                  00000a22    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000a24    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000a84    00000054     bsp_delay.o (.text.SysTick_Handler)
                  00000ad8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000b20    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000b68    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000ba8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000be4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000c20    00000038     bsp_usart.o (.text.UART0_IRQHandler)
                  00000c58    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00000c90    00000034     bsp_at8236.o (.text.L1_control)
                  00000cc4    00000034     bsp_at8236.o (.text.L2_control)
                  00000cf8    00000034     bsp_at8236.o (.text.init_motor)
                  00000d2c    00000034     bsp_usart.o (.text.uart0_send_string)
                  00000d60    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00000d90    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000db8    00000022            : memccpy.c.obj (.text.memccpy)
                  00000dda    00000002     --HOLE-- [fill = 0]
                  00000ddc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000df8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000e14    0000001c     bsp_delay.o (.text.delay_ms)
                  00000e30    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000e48    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00000e60    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000e76    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00000e88    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000e9a    00000002     --HOLE-- [fill = 0]
                  00000e9c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00000eac    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00000eba    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00000ec8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000ed2    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00000edc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000ee4    00000006     libc.a : exit.c.obj (.text:abort)
                  00000eea    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000eee    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000ef2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000ef6    00000002     --HOLE-- [fill = 0]

.cinit     0    00000f68    00000030     
                  00000f68    0000000c     (__TI_handler_table)
                  00000f74    0000000b     (.cinit..data.load) [load image, compression = lzss]
                  00000f7f    00000001     --HOLE-- [fill = 0]
                  00000f80    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000f88    00000010     (__TI_cinit_table)

.rodata    0    00000ef8    00000070     
                  00000ef8    00000023     main.o (.rodata.str1.15159059442110792349.1)
                  00000f1b    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00000f2c    0000000a     ti_msp_dl_config.o (.rodata.gMYUARTConfig)
                  00000f36    00000002     ti_msp_dl_config.o (.rodata.gMYUARTClockConfig)
                  00000f38    00000008     ti_msp_dl_config.o (.rodata.gPWM_L1Config)
                  00000f40    00000008     ti_msp_dl_config.o (.rodata.gPWM_L2Config)
                  00000f48    00000008     ti_msp_dl_config.o (.rodata.gPWM_R1Config)
                  00000f50    00000008     ti_msp_dl_config.o (.rodata.gPWM_R2Config)
                  00000f58    00000003     ti_msp_dl_config.o (.rodata.gPWM_L1ClockConfig)
                  00000f5b    00000003     ti_msp_dl_config.o (.rodata.gPWM_L2ClockConfig)
                  00000f5e    00000003     ti_msp_dl_config.o (.rodata.gPWM_R1ClockConfig)
                  00000f61    00000003     ti_msp_dl_config.o (.rodata.gPWM_R2ClockConfig)
                  00000f64    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000200     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_R2Backup)
                  202000bc    000000a0     (.common:gPWM_L2Backup)
                  2020015c    000000a0     (.common:gPWM_R1Backup)
                  202001fc    00000004     (.common:gpioA)

.data      0    20200200    0000003c     UNINITIALIZED
                  20200200    00000023     main.o (.data.buf)
                  20200223    00000001     bsp_usart.o (.data.uart_data)
                  20200224    00000004     bsp_delay.o (.data.delay_times)
                  20200228    00000004     bsp_enconder.o (.data.gEncoderCount_L1)
                  2020022c    00000004     bsp_enconder.o (.data.gEncoderCount_L2)
                  20200230    00000004     bsp_delay.o (.data.getspeed)
                  20200234    00000004     main.o (.data.speed2)
                  20200238    00000004     main.o (.data.speed)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1120   56        508    
       startup_mspm0g350x_ticlang.o   6      192       0      
       main.o                         108    35        43     
    +--+------------------------------+------+---------+---------+
       Total:                         1234   283       551    
                                                              
    .\BSP\
       bsp_enconder.o                 168    0         12     
       bsp_at8236.o                   156    0         0      
       bsp_delay.o                    112    0         8      
       bsp_usart.o                    108    0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         544    0         21     
                                                              
    D:/TI/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         456    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658    17        0      
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       memset16.S.obj                 98     0         0      
       sprintf.c.obj                  90     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1308   17        0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memset.S.obj             14     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         88     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      47        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3634   347       1084   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000f88 records: 2, size/record: 8, table size: 16
	.data: load addr=00000f74, load size=0000000b bytes, run addr=20200200, run size=0000003c bytes, compression=lzss
	.bss: load addr=00000f80, load size=00000008 bytes, run addr=20200000, run size=00000200 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000f68 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000627  ADC0_IRQHandler                 
00000627  ADC1_IRQHandler                 
00000627  AES_IRQHandler                  
00000eea  C$$EXIT                         
00000627  CANFD0_IRQHandler               
00000627  DAC0_IRQHandler                 
00000ec9  DL_Common_delayCycles           
00000345  DL_Timer_initFourCCPWMMode      
00000ddd  DL_Timer_setCaptCompUpdateMethod
00000e31  DL_Timer_setCaptureCompareOutCtl
00000e9d  DL_Timer_setCaptureCompareValue 
00000df9  DL_Timer_setClockConfig         
00000ad9  DL_UART_init                    
00000e77  DL_UART_setClockConfig          
00000627  DMA_IRQHandler                  
00000627  Default_Handler                 
00000627  GROUP0_IRQHandler               
00000449  GROUP1_IRQHandler               
00000eeb  HOSTexit                        
00000627  HardFault_Handler               
00000627  I2C0_IRQHandler                 
00000627  I2C1_IRQHandler                 
00000c91  L1_control                      
00000cc5  L2_control                      
00000627  NMI_Handler                     
00000627  PendSV_Handler                  
00000627  RTC_IRQHandler                  
00000eef  Reset_Handler                   
00000627  SPI0_IRQHandler                 
00000627  SPI1_IRQHandler                 
00000627  SVC_Handler                     
000004f1  SYSCFG_DL_GPIO_init             
000008e5  SYSCFG_DL_MYUART_init           
00000629  SYSCFG_DL_PWM_L1_init           
000006b9  SYSCFG_DL_PWM_L2_init           
00000749  SYSCFG_DL_PWM_R1_init           
000007d9  SYSCFG_DL_PWM_R2_init           
00000ba9  SYSCFG_DL_SYSCTL_init           
00000d61  SYSCFG_DL_SYSTICK_init          
00000b21  SYSCFG_DL_init                  
00000a25  SYSCFG_DL_initPower             
00000a85  SysTick_Handler                 
00000627  TIMA0_IRQHandler                
00000627  TIMA1_IRQHandler                
00000627  TIMG0_IRQHandler                
00000627  TIMG12_IRQHandler               
00000627  TIMG6_IRQHandler                
00000627  TIMG7_IRQHandler                
00000627  TIMG8_IRQHandler                
00000c21  UART0_IRQHandler                
00000627  UART1_IRQHandler                
00000627  UART2_IRQHandler                
00000627  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000f88  __TI_CINIT_Base                 
00000f98  __TI_CINIT_Limit                
00000f98  __TI_CINIT_Warm                 
00000f68  __TI_Handler_Table_Base         
00000f74  __TI_Handler_Table_Limit        
00000be5  __TI_auto_init_nobinit_nopinit  
00000869  __TI_decompress_lzss            
00000e89  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
000000c1  __TI_printfi_minimal            
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00000e61  __TI_zero_init_nomemset         
00000a23  __aeabi_idiv0                   
00000edd  __aeabi_memcpy                  
00000edd  __aeabi_memcpy4                 
00000edd  __aeabi_memcpy8                 
00000ead  __aeabi_memset                  
00000ead  __aeabi_memset4                 
00000ead  __aeabi_memset8                 
00000b69  __aeabi_uidiv                   
00000b69  __aeabi_uidivmod                
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000d91  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00000ef3  _system_pre_init                
00000ee5  abort                           
ffffffff  binit                           
20200200  buf                             
00000e15  delay_ms                        
20200224  delay_times                     
20200228  gEncoderCount_L1                
2020022c  gEncoderCount_L2                
202000bc  gPWM_L2Backup                   
2020015c  gPWM_R1Backup                   
20200000  gPWM_R2Backup                   
20200230  getspeed                        
202001fc  gpioA                           
00000cf9  init_motor                      
00000000  interruptVectors                
00000955  main                            
00000db9  memccpy                         
0000058d  memcpy                          
000009c1  memset                          
20200238  speed                           
20200234  speed2                          
00000c59  sprintf                         
00000d2d  uart0_send_string               
20200223  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __TI_printfi_minimal            
00000200  __STACK_SIZE                    
00000345  DL_Timer_initFourCCPWMMode      
00000449  GROUP1_IRQHandler               
000004f1  SYSCFG_DL_GPIO_init             
0000058d  memcpy                          
00000627  ADC0_IRQHandler                 
00000627  ADC1_IRQHandler                 
00000627  AES_IRQHandler                  
00000627  CANFD0_IRQHandler               
00000627  DAC0_IRQHandler                 
00000627  DMA_IRQHandler                  
00000627  Default_Handler                 
00000627  GROUP0_IRQHandler               
00000627  HardFault_Handler               
00000627  I2C0_IRQHandler                 
00000627  I2C1_IRQHandler                 
00000627  NMI_Handler                     
00000627  PendSV_Handler                  
00000627  RTC_IRQHandler                  
00000627  SPI0_IRQHandler                 
00000627  SPI1_IRQHandler                 
00000627  SVC_Handler                     
00000627  TIMA0_IRQHandler                
00000627  TIMA1_IRQHandler                
00000627  TIMG0_IRQHandler                
00000627  TIMG12_IRQHandler               
00000627  TIMG6_IRQHandler                
00000627  TIMG7_IRQHandler                
00000627  TIMG8_IRQHandler                
00000627  UART1_IRQHandler                
00000627  UART2_IRQHandler                
00000627  UART3_IRQHandler                
00000629  SYSCFG_DL_PWM_L1_init           
000006b9  SYSCFG_DL_PWM_L2_init           
00000749  SYSCFG_DL_PWM_R1_init           
000007d9  SYSCFG_DL_PWM_R2_init           
00000869  __TI_decompress_lzss            
000008e5  SYSCFG_DL_MYUART_init           
00000955  main                            
000009c1  memset                          
00000a23  __aeabi_idiv0                   
00000a25  SYSCFG_DL_initPower             
00000a85  SysTick_Handler                 
00000ad9  DL_UART_init                    
00000b21  SYSCFG_DL_init                  
00000b69  __aeabi_uidiv                   
00000b69  __aeabi_uidivmod                
00000ba9  SYSCFG_DL_SYSCTL_init           
00000be5  __TI_auto_init_nobinit_nopinit  
00000c21  UART0_IRQHandler                
00000c59  sprintf                         
00000c91  L1_control                      
00000cc5  L2_control                      
00000cf9  init_motor                      
00000d2d  uart0_send_string               
00000d61  SYSCFG_DL_SYSTICK_init          
00000d91  _c_int00_noargs                 
00000db9  memccpy                         
00000ddd  DL_Timer_setCaptCompUpdateMethod
00000df9  DL_Timer_setClockConfig         
00000e15  delay_ms                        
00000e31  DL_Timer_setCaptureCompareOutCtl
00000e61  __TI_zero_init_nomemset         
00000e77  DL_UART_setClockConfig          
00000e89  __TI_decompress_none            
00000e9d  DL_Timer_setCaptureCompareValue 
00000ead  __aeabi_memset                  
00000ead  __aeabi_memset4                 
00000ead  __aeabi_memset8                 
00000ec9  DL_Common_delayCycles           
00000edd  __aeabi_memcpy                  
00000edd  __aeabi_memcpy4                 
00000edd  __aeabi_memcpy8                 
00000ee5  abort                           
00000eea  C$$EXIT                         
00000eeb  HOSTexit                        
00000eef  Reset_Handler                   
00000ef3  _system_pre_init                
00000f68  __TI_Handler_Table_Base         
00000f74  __TI_Handler_Table_Limit        
00000f88  __TI_CINIT_Base                 
00000f98  __TI_CINIT_Limit                
00000f98  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_R2Backup                   
202000bc  gPWM_L2Backup                   
2020015c  gPWM_R1Backup                   
202001fc  gpioA                           
20200200  buf                             
20200223  uart_data                       
20200224  delay_times                     
20200228  gEncoderCount_L1                
2020022c  gEncoderCount_L2                
20200230  getspeed                        
20200234  speed2                          
20200238  speed                           
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[120 symbols]
