#include "bsp_delay.h"
#include "ti_msp_dl_config.h"

volatile unsigned int delay_times = 0;

volatile unsigned int getspeed = 10;//获取速度标志 10ms获取一次

extern volatile int32_t gEncoderCount_L1,gEncoderCount_L2;
extern int speed,speed2;

//搭配滴答定时器实现的精确ms延时
void delay_ms(unsigned int ms)//1ms定时
{
    delay_times = ms;
    while( delay_times != 0 );
}


//滴答定时器中断服务函数
void SysTick_Handler(void)
{
    if( delay_times != 0 )
    {
        delay_times--;
    }
		
		 if( getspeed != 0 )
		{
				getspeed--;
		}
		else
		{
			getspeed = 10;
			speed = gEncoderCount_L1;
			speed2 = gEncoderCount_L2;
			gEncoderCount_L1 = 0;//清0
			gEncoderCount_L2 = 0;//清0
		}
		
		
}