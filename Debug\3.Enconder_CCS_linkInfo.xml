<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o 3.Enconder_CCS.out -m3.Enconder_CCS.map -iD:/TI/mspm0_sdk_2_05_00_05/source -iD:/TI/3.Enconder_CCS -iD:/TI/3.Enconder_CCS/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=3.Enconder_CCS_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./BSP/bsp_at8236.o ./BSP/bsp_delay.o ./BSP/bsp_enconder.o ./BSP/bsp_usart.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x688af56b</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\TI\3.Enconder_CCS\Debug\3.Enconder_CCS.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xd91</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\TI\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\TI\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\TI\3.Enconder_CCS\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\TI\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_at8236.o</file>
         <name>bsp_at8236.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\TI\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_delay.o</file>
         <name>bsp_delay.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\TI\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_enconder.o</file>
         <name>bsp_enconder.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\TI\3.Enconder_CCS\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>bsp_usart.o</file>
         <name>bsp_usart.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>D:\TI\3.Enconder_CCS\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-ec">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-ed">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ee">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-ef">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-f0">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-f1">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text:__TI_printfi_minimal</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x284</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x344</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x448</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x448</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x4f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f0</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text:memcpy</name>
         <load_address>0x58c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x58c</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x626</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x626</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_PWM_L1_init</name>
         <load_address>0x628</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x628</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_PWM_L2_init</name>
         <load_address>0x6b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.SYSCFG_DL_PWM_R1_init</name>
         <load_address>0x748</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x748</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_PWM_R2_init</name>
         <load_address>0x7d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d8</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x868</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_MYUART_init</name>
         <load_address>0x8e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8e4</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.main</name>
         <load_address>0x954</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x954</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text:memset</name>
         <load_address>0x9c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c0</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xa22</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa22</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xa24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa24</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0xa84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa84</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_UART_init</name>
         <load_address>0xad8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xad8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xb20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb20</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-139">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xb68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb68</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xba8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xbe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbe4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0xc20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc20</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.sprintf</name>
         <load_address>0xc58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc58</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.L1_control</name>
         <load_address>0xc90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc90</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.L2_control</name>
         <load_address>0xcc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.init_motor</name>
         <load_address>0xcf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcf8</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.uart0_send_string</name>
         <load_address>0xd2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd2c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0xd60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd60</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xd90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd90</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.memccpy</name>
         <load_address>0xdb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdb8</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0xddc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xddc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xdf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.delay_ms</name>
         <load_address>0xe14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe14</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0xe30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe30</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text._outs</name>
         <load_address>0xe48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe48</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xe60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe60</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0xe76</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe76</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xe88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe88</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0xe9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe9c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.__aeabi_memset</name>
         <load_address>0xeac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeac</run_address>
         <size>0xe</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.strlen</name>
         <load_address>0xeba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeba</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xec8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text._outc</name>
         <load_address>0xed2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed2</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xedc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xedc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text:abort</name>
         <load_address>0xee4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xee4</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.HOSTexit</name>
         <load_address>0xeea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeea</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xeee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeee</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.text._system_pre_init</name>
         <load_address>0xef2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xef2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-20b">
         <name>__TI_handler_table</name>
         <load_address>0xf68</load_address>
         <readonly>true</readonly>
         <run_address>0xf68</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-20d">
         <name>.cinit..data.load</name>
         <load_address>0xf74</load_address>
         <readonly>true</readonly>
         <run_address>0xf74</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-20e">
         <name>.cinit..bss.load</name>
         <load_address>0xf80</load_address>
         <readonly>true</readonly>
         <run_address>0xf80</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-20c">
         <name>__TI_cinit_table</name>
         <load_address>0xf88</load_address>
         <readonly>true</readonly>
         <run_address>0xf88</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b1">
         <name>.rodata.str1.15159059442110792349.1</name>
         <load_address>0xef8</load_address>
         <readonly>true</readonly>
         <run_address>0xef8</run_address>
         <size>0x23</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-138">
         <name>.rodata.str1.15363888844622738466.1</name>
         <load_address>0xf1b</load_address>
         <readonly>true</readonly>
         <run_address>0xf1b</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-117">
         <name>.rodata.gMYUARTConfig</name>
         <load_address>0xf2c</load_address>
         <readonly>true</readonly>
         <run_address>0xf2c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-116">
         <name>.rodata.gMYUARTClockConfig</name>
         <load_address>0xf36</load_address>
         <readonly>true</readonly>
         <run_address>0xf36</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-108">
         <name>.rodata.gPWM_L1Config</name>
         <load_address>0xf38</load_address>
         <readonly>true</readonly>
         <run_address>0xf38</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.rodata.gPWM_L2Config</name>
         <load_address>0xf40</load_address>
         <readonly>true</readonly>
         <run_address>0xf40</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.rodata.gPWM_R1Config</name>
         <load_address>0xf48</load_address>
         <readonly>true</readonly>
         <run_address>0xf48</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.rodata.gPWM_R2Config</name>
         <load_address>0xf50</load_address>
         <readonly>true</readonly>
         <run_address>0xf50</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.rodata.gPWM_L1ClockConfig</name>
         <load_address>0xf58</load_address>
         <readonly>true</readonly>
         <run_address>0xf58</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.rodata.gPWM_L2ClockConfig</name>
         <load_address>0xf5b</load_address>
         <readonly>true</readonly>
         <run_address>0xf5b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-109">
         <name>.rodata.gPWM_R1ClockConfig</name>
         <load_address>0xf5e</load_address>
         <readonly>true</readonly>
         <run_address>0xf5e</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.rodata.gPWM_R2ClockConfig</name>
         <load_address>0xf61</load_address>
         <readonly>true</readonly>
         <run_address>0xf61</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-67">
         <name>.data.speed</name>
         <load_address>0x20200238</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200238</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.data.speed2</name>
         <load_address>0x20200234</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200234</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.data.buf</name>
         <load_address>0x20200200</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200200</run_address>
         <size>0x23</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-64">
         <name>.data.delay_times</name>
         <load_address>0x20200224</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200224</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-65">
         <name>.data.getspeed</name>
         <load_address>0x20200230</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200230</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-66">
         <name>.data.gEncoderCount_L1</name>
         <load_address>0x20200228</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200228</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.data.gEncoderCount_L2</name>
         <load_address>0x2020022c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020022c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-71">
         <name>.data.uart_data</name>
         <load_address>0x20200223</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200223</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.common:gPWM_R1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020015c</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-dd">
         <name>.common:gPWM_L2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.common:gPWM_R2Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6e">
         <name>.common:gpioA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001fc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-210">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_loc</name>
         <load_address>0x186</load_address>
         <run_address>0x186</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_loc</name>
         <load_address>0x31e</load_address>
         <run_address>0x31e</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_loc</name>
         <load_address>0x346</load_address>
         <run_address>0x346</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_loc</name>
         <load_address>0x36c</load_address>
         <run_address>0x36c</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_loc</name>
         <load_address>0x37f</load_address>
         <run_address>0x37f</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_loc</name>
         <load_address>0x1da6</load_address>
         <run_address>0x1da6</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_loc</name>
         <load_address>0x2562</load_address>
         <run_address>0x2562</run_address>
         <size>0x136</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_loc</name>
         <load_address>0x2698</load_address>
         <run_address>0x2698</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_loc</name>
         <load_address>0x2770</load_address>
         <run_address>0x2770</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_loc</name>
         <load_address>0x2b94</load_address>
         <run_address>0x2b94</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_loc</name>
         <load_address>0x2d00</load_address>
         <run_address>0x2d00</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x2d6f</load_address>
         <run_address>0x2d6f</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_loc</name>
         <load_address>0x2ed6</load_address>
         <run_address>0x2ed6</run_address>
         <size>0x32d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x61ae</load_address>
         <run_address>0x61ae</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_loc</name>
         <load_address>0x61d4</load_address>
         <run_address>0x61d4</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.debug_loc</name>
         <load_address>0x6293</load_address>
         <run_address>0x6293</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_abbrev</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_abbrev</name>
         <load_address>0x2c5</load_address>
         <run_address>0x2c5</run_address>
         <size>0x110</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x3d5</load_address>
         <run_address>0x3d5</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_abbrev</name>
         <load_address>0x4ed</load_address>
         <run_address>0x4ed</run_address>
         <size>0x76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_abbrev</name>
         <load_address>0x563</load_address>
         <run_address>0x563</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_abbrev</name>
         <load_address>0x66a</load_address>
         <run_address>0x66a</run_address>
         <size>0x172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x7dc</load_address>
         <run_address>0x7dc</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_abbrev</name>
         <load_address>0x83e</load_address>
         <run_address>0x83e</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0xac4</load_address>
         <run_address>0xac4</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0xd5f</load_address>
         <run_address>0xd5f</run_address>
         <size>0xe1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_abbrev</name>
         <load_address>0xe40</load_address>
         <run_address>0xe40</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_abbrev</name>
         <load_address>0xeef</load_address>
         <run_address>0xeef</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_abbrev</name>
         <load_address>0x105f</load_address>
         <run_address>0x105f</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_abbrev</name>
         <load_address>0x1098</load_address>
         <run_address>0x1098</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_abbrev</name>
         <load_address>0x115a</load_address>
         <run_address>0x115a</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x11ca</load_address>
         <run_address>0x11ca</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_abbrev</name>
         <load_address>0x1257</load_address>
         <run_address>0x1257</run_address>
         <size>0x2a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0x14fa</load_address>
         <run_address>0x14fa</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0x1592</load_address>
         <run_address>0x1592</run_address>
         <size>0x8b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_abbrev</name>
         <load_address>0x161d</load_address>
         <run_address>0x161d</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_abbrev</name>
         <load_address>0x1649</load_address>
         <run_address>0x1649</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_abbrev</name>
         <load_address>0x1670</load_address>
         <run_address>0x1670</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_abbrev</name>
         <load_address>0x1695</load_address>
         <run_address>0x1695</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.debug_abbrev</name>
         <load_address>0x16bc</load_address>
         <run_address>0x16bc</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x1715</load_address>
         <run_address>0x1715</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_abbrev</name>
         <load_address>0x173a</load_address>
         <run_address>0x173a</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-212">
         <name>.debug_abbrev</name>
         <load_address>0x175f</load_address>
         <run_address>0x175f</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3596</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3596</load_address>
         <run_address>0x3596</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_info</name>
         <load_address>0x3616</load_address>
         <run_address>0x3616</run_address>
         <size>0x36a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0x3980</load_address>
         <run_address>0x3980</run_address>
         <size>0x8d2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x4252</load_address>
         <run_address>0x4252</run_address>
         <size>0x8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_info</name>
         <load_address>0x42e1</load_address>
         <run_address>0x42e1</run_address>
         <size>0x87b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_info</name>
         <load_address>0x4b5c</load_address>
         <run_address>0x4b5c</run_address>
         <size>0x77e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_info</name>
         <load_address>0x52da</load_address>
         <run_address>0x52da</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_info</name>
         <load_address>0x534f</load_address>
         <run_address>0x534f</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_info</name>
         <load_address>0x84c1</load_address>
         <run_address>0x84c1</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_info</name>
         <load_address>0x9767</load_address>
         <run_address>0x9767</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x98cc</load_address>
         <run_address>0x98cc</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x9cef</load_address>
         <run_address>0x9cef</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0xa433</load_address>
         <run_address>0xa433</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_info</name>
         <load_address>0xa479</load_address>
         <run_address>0xa479</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xa60b</load_address>
         <run_address>0xa60b</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xa6d1</load_address>
         <run_address>0xa6d1</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_info</name>
         <load_address>0xa84d</load_address>
         <run_address>0xa84d</run_address>
         <size>0x1f24</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_info</name>
         <load_address>0xc771</load_address>
         <run_address>0xc771</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_info</name>
         <load_address>0xc869</load_address>
         <run_address>0xc869</run_address>
         <size>0xce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_info</name>
         <load_address>0xc937</load_address>
         <run_address>0xc937</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0xc972</load_address>
         <run_address>0xc972</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0xcb0b</load_address>
         <run_address>0xcb0b</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_info</name>
         <load_address>0xccc0</load_address>
         <run_address>0xccc0</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_info</name>
         <load_address>0xce7c</load_address>
         <run_address>0xce7c</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_info</name>
         <load_address>0xcf01</load_address>
         <run_address>0xcf01</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_info</name>
         <load_address>0xd1fb</load_address>
         <run_address>0xd1fb</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-211">
         <name>.debug_info</name>
         <load_address>0xd43f</load_address>
         <run_address>0xd43f</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_ranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_ranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_ranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_ranges</name>
         <load_address>0x338</load_address>
         <run_address>0x338</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_ranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_ranges</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_ranges</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_ranges</name>
         <load_address>0x590</load_address>
         <run_address>0x590</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_ranges</name>
         <load_address>0x5a8</load_address>
         <run_address>0x5a8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_ranges</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_ranges</name>
         <load_address>0x770</load_address>
         <run_address>0x770</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_ranges</name>
         <load_address>0x788</load_address>
         <run_address>0x788</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_ranges</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_ranges</name>
         <load_address>0x7c8</load_address>
         <run_address>0x7c8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_ranges</name>
         <load_address>0x7f0</load_address>
         <run_address>0x7f0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2549</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_str</name>
         <load_address>0x2549</load_address>
         <run_address>0x2549</run_address>
         <size>0x145</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_str</name>
         <load_address>0x268e</load_address>
         <run_address>0x268e</run_address>
         <size>0x41d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x2aab</load_address>
         <run_address>0x2aab</run_address>
         <size>0x4bd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_str</name>
         <load_address>0x2f68</load_address>
         <run_address>0x2f68</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_str</name>
         <load_address>0x304c</load_address>
         <run_address>0x304c</run_address>
         <size>0x4c0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_str</name>
         <load_address>0x350c</load_address>
         <run_address>0x350c</run_address>
         <size>0x55e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_str</name>
         <load_address>0x3a6a</load_address>
         <run_address>0x3a6a</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_str</name>
         <load_address>0x3be1</load_address>
         <run_address>0x3be1</run_address>
         <size>0x1dd6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_str</name>
         <load_address>0x59b7</load_address>
         <run_address>0x59b7</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x66a4</load_address>
         <run_address>0x66a4</run_address>
         <size>0x164</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_str</name>
         <load_address>0x6808</load_address>
         <run_address>0x6808</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_str</name>
         <load_address>0x6a2d</load_address>
         <run_address>0x6a2d</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_str</name>
         <load_address>0x6d5c</load_address>
         <run_address>0x6d5c</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_str</name>
         <load_address>0x6e51</load_address>
         <run_address>0x6e51</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_str</name>
         <load_address>0x6fec</load_address>
         <run_address>0x6fec</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x7154</load_address>
         <run_address>0x7154</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_str</name>
         <load_address>0x7329</load_address>
         <run_address>0x7329</run_address>
         <size>0x8f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_str</name>
         <load_address>0x7c22</load_address>
         <run_address>0x7c22</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_str</name>
         <load_address>0x7d6a</load_address>
         <run_address>0x7d6a</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_str</name>
         <load_address>0x7e91</load_address>
         <run_address>0x7e91</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.debug_str</name>
         <load_address>0x7f7a</load_address>
         <run_address>0x7f7a</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x154</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x154</load_address>
         <run_address>0x154</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x184</load_address>
         <run_address>0x184</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0x1a4</load_address>
         <run_address>0x1a4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x234</load_address>
         <run_address>0x234</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0x264</load_address>
         <run_address>0x264</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_frame</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_frame</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_frame</name>
         <load_address>0x2f0</load_address>
         <run_address>0x2f0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_frame</name>
         <load_address>0x6f8</load_address>
         <run_address>0x6f8</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x8b0</load_address>
         <run_address>0x8b0</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_frame</name>
         <load_address>0x908</load_address>
         <run_address>0x908</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0x998</load_address>
         <run_address>0x998</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_frame</name>
         <load_address>0xa98</load_address>
         <run_address>0xa98</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0xab8</load_address>
         <run_address>0xab8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0xaf0</load_address>
         <run_address>0xaf0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0xb18</load_address>
         <run_address>0xb18</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_frame</name>
         <load_address>0xb48</load_address>
         <run_address>0xb48</run_address>
         <size>0x480</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0xfc8</load_address>
         <run_address>0xfc8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_frame</name>
         <load_address>0xff8</load_address>
         <run_address>0xff8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_frame</name>
         <load_address>0x1024</load_address>
         <run_address>0x1024</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_frame</name>
         <load_address>0x1044</load_address>
         <run_address>0x1044</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x7d0</load_address>
         <run_address>0x7d0</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x888</load_address>
         <run_address>0x888</run_address>
         <size>0x1c9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_line</name>
         <load_address>0xa51</load_address>
         <run_address>0xa51</run_address>
         <size>0x2a9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0xcfa</load_address>
         <run_address>0xcfa</run_address>
         <size>0xab</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0xda5</load_address>
         <run_address>0xda5</run_address>
         <size>0x285</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_line</name>
         <load_address>0x102a</load_address>
         <run_address>0x102a</run_address>
         <size>0x2df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_line</name>
         <load_address>0x1309</load_address>
         <run_address>0x1309</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_line</name>
         <load_address>0x1482</load_address>
         <run_address>0x1482</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_line</name>
         <load_address>0x2bf1</load_address>
         <run_address>0x2bf1</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_line</name>
         <load_address>0x3609</load_address>
         <run_address>0x3609</run_address>
         <size>0x111</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x371a</load_address>
         <run_address>0x371a</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0x38f6</load_address>
         <run_address>0x38f6</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_line</name>
         <load_address>0x3e10</load_address>
         <run_address>0x3e10</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_line</name>
         <load_address>0x3e4e</load_address>
         <run_address>0x3e4e</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x3f4c</load_address>
         <run_address>0x3f4c</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x400c</load_address>
         <run_address>0x400c</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_line</name>
         <load_address>0x41d4</load_address>
         <run_address>0x41d4</run_address>
         <size>0x1c90</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x5e64</load_address>
         <run_address>0x5e64</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0x5ecb</load_address>
         <run_address>0x5ecb</run_address>
         <size>0xcf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x5f9a</load_address>
         <run_address>0x5f9a</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x5fdb</load_address>
         <run_address>0x5fdb</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_line</name>
         <load_address>0x607f</load_address>
         <run_address>0x607f</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_line</name>
         <load_address>0x6139</load_address>
         <run_address>0x6139</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_line</name>
         <load_address>0x61fb</load_address>
         <run_address>0x61fb</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_line</name>
         <load_address>0x62b0</load_address>
         <run_address>0x62b0</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_line</name>
         <load_address>0x6350</load_address>
         <run_address>0x6350</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_aranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_aranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xe38</size>
         <contents>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-7e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xf68</load_address>
         <run_address>0xf68</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-20b"/>
            <object_component_ref idref="oc-20d"/>
            <object_component_ref idref="oc-20e"/>
            <object_component_ref idref="oc-20c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xef8</load_address>
         <run_address>0xef8</run_address>
         <size>0x70</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1d3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200200</run_address>
         <size>0x3c</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-71"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-210"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ca" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1cb" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1cc" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1cd" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ce" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1cf" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d1" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ed" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x62b3</size>
         <contents>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-1ac"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ef" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x176e</size>
         <contents>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-212"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f1" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd4cd</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-211"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f3" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x818</size>
         <contents>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f5" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x810d</size>
         <contents>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-1ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f7" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1074</size>
         <contents>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-163"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f9" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x63d0</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-205" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb8</size>
         <contents>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20f" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-218" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf98</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-219" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x23c</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-21a" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0xf98</used_space>
         <unused_space>0x7068</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xe38</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xef8</start_address>
               <size>0x70</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xf68</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xf98</start_address>
               <size>0x7068</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x43c</used_space>
         <unused_space>0x3bc4</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1cf"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1d1"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200200</start_address>
               <size>0x3c</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020023c</start_address>
               <size>0x3bc4</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0xf74</load_address>
            <load_size>0xb</load_size>
            <run_address>0x20200200</run_address>
            <run_size>0x3c</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0xf80</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x200</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xf88</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xf98</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xf98</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xf68</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xf74</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6c">
         <name>SYSCFG_DL_init</name>
         <value>0xb21</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-6d">
         <name>SYSCFG_DL_initPower</name>
         <value>0xa25</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x4f1</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xba9</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-70">
         <name>SYSCFG_DL_PWM_L1_init</name>
         <value>0x629</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-71">
         <name>SYSCFG_DL_PWM_R1_init</name>
         <value>0x749</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-72">
         <name>SYSCFG_DL_PWM_L2_init</name>
         <value>0x6b9</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-73">
         <name>SYSCFG_DL_PWM_R2_init</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-74">
         <name>SYSCFG_DL_MYUART_init</name>
         <value>0x8e5</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-75">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0xd61</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-76">
         <name>gPWM_R1Backup</name>
         <value>0x2020015c</value>
      </symbol>
      <symbol id="sm-77">
         <name>gPWM_L2Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-78">
         <name>gPWM_R2Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-83">
         <name>Default_Handler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>Reset_Handler</name>
         <value>0xeef</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-85">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-86">
         <name>NMI_Handler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>HardFault_Handler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>SVC_Handler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>PendSV_Handler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>GROUP0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMG8_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>UART3_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>ADC0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>ADC1_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>CANFD0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>DAC0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>SPI0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>SPI1_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>UART1_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>UART2_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>TIMG0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG6_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>TIMA0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>TIMA1_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>TIMG7_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>TIMG12_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>I2C0_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>I2C1_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>AES_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>RTC_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>DMA_IRQHandler</name>
         <value>0x627</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a9">
         <name>main</name>
         <value>0x955</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-aa">
         <name>speed</name>
         <value>0x20200238</value>
         <object_component_ref idref="oc-67"/>
      </symbol>
      <symbol id="sm-ab">
         <name>speed2</name>
         <value>0x20200234</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-ac">
         <name>buf</name>
         <value>0x20200200</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-bd">
         <name>init_motor</name>
         <value>0xcf9</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-be">
         <name>L1_control</name>
         <value>0xc91</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-bf">
         <name>L2_control</name>
         <value>0xcc5</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-cd">
         <name>delay_ms</name>
         <value>0xe15</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-ce">
         <name>delay_times</name>
         <value>0x20200224</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-cf">
         <name>SysTick_Handler</name>
         <value>0xa85</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-d0">
         <name>getspeed</name>
         <value>0x20200230</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-d9">
         <name>GROUP1_IRQHandler</name>
         <value>0x449</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-da">
         <name>gpioA</name>
         <value>0x202001fc</value>
      </symbol>
      <symbol id="sm-db">
         <name>gEncoderCount_L1</name>
         <value>0x20200228</value>
         <object_component_ref idref="oc-66"/>
      </symbol>
      <symbol id="sm-dc">
         <name>gEncoderCount_L2</name>
         <value>0x2020022c</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-ea">
         <name>uart0_send_string</name>
         <value>0xd2d</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-eb">
         <name>UART0_IRQHandler</name>
         <value>0xc21</value>
         <object_component_ref idref="oc-3d"/>
      </symbol>
      <symbol id="sm-ec">
         <name>uart_data</name>
         <value>0x20200223</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-ed">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ee">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f0">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f1">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f2">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f3">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f4">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f5">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fe">
         <name>DL_Common_delayCycles</name>
         <value>0xec9</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-115">
         <name>DL_Timer_setClockConfig</name>
         <value>0xdf9</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-116">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0xe9d</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-117">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0xddd</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-118">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0xe31</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-119">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x345</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-126">
         <name>DL_UART_init</name>
         <value>0xad9</value>
         <object_component_ref idref="oc-115"/>
      </symbol>
      <symbol id="sm-127">
         <name>DL_UART_setClockConfig</name>
         <value>0xe77</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-138">
         <name>sprintf</name>
         <value>0xc59</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-143">
         <name>_c_int00_noargs</name>
         <value>0xd91</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-144">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-150">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xbe5</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-158">
         <name>_system_pre_init</name>
         <value>0xef3</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-163">
         <name>__TI_zero_init_nomemset</name>
         <value>0xe61</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-16c">
         <name>__TI_decompress_none</name>
         <value>0xe89</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-177">
         <name>__TI_decompress_lzss</name>
         <value>0x869</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-18d">
         <name>__TI_printfi_minimal</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-19b">
         <name>abort</name>
         <value>0xee5</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>memccpy</name>
         <value>0xdb9</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-1ba">
         <name>HOSTexit</name>
         <value>0xeeb</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1bb">
         <name>C$$EXIT</name>
         <value>0xeea</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1d0">
         <name>__aeabi_memcpy</name>
         <value>0xedd</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1d1">
         <name>__aeabi_memcpy4</name>
         <value>0xedd</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1d2">
         <name>__aeabi_memcpy8</name>
         <value>0xedd</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-1d9">
         <name>__aeabi_memset</name>
         <value>0xead</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-1da">
         <name>__aeabi_memset4</name>
         <value>0xead</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-1db">
         <name>__aeabi_memset8</name>
         <value>0xead</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-1e1">
         <name>__aeabi_uidiv</name>
         <value>0xb69</value>
         <object_component_ref idref="oc-139"/>
      </symbol>
      <symbol id="sm-1e2">
         <name>__aeabi_uidivmod</name>
         <value>0xb69</value>
         <object_component_ref idref="oc-139"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>__aeabi_idiv0</name>
         <value>0xa23</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-207">
         <name>memcpy</name>
         <value>0x58d</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-216">
         <name>memset</name>
         <value>0x9c1</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-217">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-21a">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-21b">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
